# Script Tool Documentation

The Script Tool provides secure, cross-platform script execution capabilities with predefined scripts and custom script support. It includes safety constraints, timeout management, and comprehensive error handling for Windows, macOS, and Linux.

## Overview

The `ScriptTool` class offers four main capabilities:
1. **Predefined Scripts**: Execute pre-approved, platform-specific scripts for common tasks
2. **Custom Scripts**: Execute user-provided scripts with safety constraints
3. **Cross-Platform Support**: Automatic platform detection and appropriate script execution
4. **Script Management**: List and manage available predefined scripts and supported languages

## Available Methods

### 1. `script_tool.list_predefined_scripts`

List all available predefined scripts with descriptions and commands.

**Parameters:** None

**Example Usage:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "script_tool.list_predefined_scripts",
    "arguments": {}
  }
}
```

**Response:**
```
Available Predefined Scripts:

• system_info
  Description: Get system information
  Command: python3 -c "import platform, psutil; print(f'OS: {platform.system()} {platform.release()}'); print(f'CPU: {psutil.cpu_percent()}% usage'); print(f'Memory: {psutil.virtual_memory().percent}% usage')"

• disk_usage
  Description: Check disk usage
  Command: df -h

• weather_test
  Description: Test weather API connectivity
  Command: python3 -c "import requests; r = requests.get('https://api.weather.gov/alerts/active/area/CA', timeout=10); print(f'Status: {r.status_code}'); print(f'Response size: {len(r.text)} bytes')"

• network_test
  Description: Test network connectivity
  Command: ping -c 3 *******

• date_time
  Description: Get current date and time
  Command: date
```

### 2. `script_tool.run_predefined_script`

Execute a predefined script by name with optional timeout configuration.

**Parameters:**
- `script_name` (string, required): Name of the predefined script to execute
- `timeout` (integer, optional): Maximum execution time in seconds (default: 30)

**Example Usage:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "script_tool.run_predefined_script",
    "arguments": {
      "script_name": "system_info",
      "timeout": 60
    }
  }
}
```

**Response:**
```
Script: system_info
Description: Get system information
Return Code: 0
Status: ✅ Success

Output:
OS: Darwin 23.1.0
CPU: 15.2% usage
Memory: 45.8% usage
```

### 3. `script_tool.run_custom_script`

Execute a custom script with safety constraints and language support.

**Parameters:**
- `script_content` (string, required): The script content to execute
- `language` (string, optional): Script language - `bash`, `python3`, or `node` (default: `bash`)
- `timeout` (integer, optional): Maximum execution time in seconds (default: 30)

**Example Usage:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "script_tool.run_custom_script",
    "arguments": {
      "script_content": "echo 'Hello, World!'\ndate\necho 'Script completed'",
      "language": "bash",
      "timeout": 30
    }
  }
}
```

**Response:**
```
Custom Script (bash)
Return Code: 0
Status: ✅ Success

Output:
Hello, World!
Mon Dec  4 10:30:45 PST 2023
Script completed
```

## Predefined Scripts

### Available Scripts

#### 1. `system_info`
- **Description**: Get comprehensive system information
- **Output**: Operating system, CPU usage, memory usage
- **Use Case**: System monitoring and diagnostics

#### 2. `disk_usage`
- **Description**: Check disk space usage
- **Output**: Disk usage statistics in human-readable format
- **Use Case**: Storage monitoring

#### 3. `weather_test`
- **Description**: Test weather API connectivity
- **Output**: API response status and data size
- **Use Case**: Network connectivity testing

#### 4. `network_test`
- **Description**: Test basic network connectivity
- **Output**: Ping results to Google DNS (*******)
- **Use Case**: Network diagnostics

#### 5. `date_time`
- **Description**: Get current date and time
- **Output**: Current system date and time
- **Use Case**: Time synchronization checks

## Custom Script Support

### Supported Languages

#### 1. Bash (`bash`)
- **Default language** for shell commands
- **Use Case**: System administration, file operations, command chaining
- **Example**:
  ```bash
  #!/bin/bash
  echo "Current directory: $(pwd)"
  ls -la
  echo "Total files: $(ls -1 | wc -l)"
  ```

#### 2. Python 3 (`python3`)
- **Use Case**: Data processing, API calls, complex logic
- **Example**:
  ```python
  import json
  import datetime
  
  data = {
      "timestamp": datetime.datetime.now().isoformat(),
      "message": "Hello from Python!"
  }
  print(json.dumps(data, indent=2))
  ```

#### 3. Node.js (`node`)
- **Use Case**: JavaScript execution, npm package usage
- **Example**:
  ```javascript
  const fs = require('fs');
  const path = require('path');
  
  console.log('Node.js version:', process.version);
  console.log('Current working directory:', process.cwd());
  ```

## Safety Features

### Security Constraints

The script tool implements multiple layers of security:

#### 1. Dangerous Command Detection
Blocks scripts containing potentially harmful commands:
- `rm -rf`, `rm -f /` - Destructive file operations
- `mkfs`, `fdisk`, `format` - Disk formatting operations
- `dd if=` - Low-level disk operations
- `sudo rm`, `sudo dd` - Privileged destructive operations
- `chmod 777` - Dangerous permission changes
- `> /dev/` - Device file manipulation
- `curl.*|.*sh`, `wget.*|.*sh` - Remote script execution
- `eval`, `exec` - Dynamic code execution
- `system(`, `os.system` - System command execution
- `subprocess.call` - Process spawning
- `__import__`, `compile` - Dynamic imports and compilation

#### 2. Script Size Limits
- **Maximum size**: 10KB per script
- **Purpose**: Prevent resource exhaustion and large malicious payloads

#### 3. Execution Timeouts
- **Default timeout**: 30 seconds
- **Configurable**: Can be adjusted per script execution
- **Automatic termination**: Processes are killed if they exceed timeout

#### 4. Output Limits
- **Maximum output**: 1MB per execution
- **Purpose**: Prevent memory exhaustion from verbose scripts

### Resource Management

#### Process Isolation
- Scripts run in separate processes
- Automatic cleanup of temporary files
- Process termination on timeout

#### Temporary File Handling
- Scripts are written to temporary files
- Automatic cleanup after execution
- Secure file permissions

## Error Handling

### Standard Error Format

All script executions return consistent error information:

```json
{
  "success": false,
  "return_code": -1,
  "stdout": "",
  "stderr": "Error description",
  "timeout": false
}
```

### Common Error Types

#### 1. Security Violations
```
❌ Script rejected: Contains potentially dangerous command 'rm -rf'
```

#### 2. Size Limitations
```
❌ Script rejected: Script too large (max 10KB)
```

#### 3. Timeout Errors
```
Status: ❌ Failed
⏰ Script timed out after 30 seconds
```

#### 4. Language Support
```
❌ Unsupported language: ruby. Supported: bash, python3, node
```

#### 5. Script Not Found
```
Script 'invalid_script' not found. Available scripts: system_info, disk_usage, weather_test, network_test, date_time
```

## Usage Examples

### Basic System Information
```json
{
  "name": "script_tool.run_predefined_script",
  "arguments": {
    "script_name": "system_info"
  }
}
```

### Custom Python Data Processing
```json
{
  "name": "script_tool.run_custom_script",
  "arguments": {
    "script_content": "import json\ndata = {'status': 'success', 'count': 42}\nprint(json.dumps(data))",
    "language": "python3",
    "timeout": 60
  }
}
```

### Network Connectivity Check
```json
{
  "name": "script_tool.run_predefined_script",
  "arguments": {
    "script_name": "network_test",
    "timeout": 45
  }
}
```

### File System Operations
```json
{
  "name": "script_tool.run_custom_script",
  "arguments": {
    "script_content": "echo 'Listing current directory:'\nls -la\necho 'Disk usage:'\ndu -sh .",
    "language": "bash"
  }
}
```

## Best Practices

### Script Development

#### 1. Use Predefined Scripts When Possible
- Predefined scripts are pre-vetted for security
- Faster execution (no safety checks)
- Consistent, reliable results

#### 2. Keep Custom Scripts Simple
- Focus on single tasks
- Avoid complex logic that might timeout
- Use appropriate language for the task

#### 3. Handle Errors Gracefully
```bash
# Good: Check if command exists
if command -v python3 &> /dev/null; then
    python3 --version
else
    echo "Python 3 not found"
fi
```

#### 4. Provide Clear Output
```python
# Good: Structured output
import json
result = {
    "status": "success",
    "data": {"count": 42},
    "timestamp": "2023-12-04T10:30:45Z"
}
print(json.dumps(result, indent=2))
```

### Security Considerations

#### 1. Avoid Sensitive Operations
- Don't include passwords or API keys in scripts
- Avoid operations that modify system configuration
- Use read-only operations when possible

#### 2. Validate Input Data
```python
# Good: Validate input
import sys
if len(sys.argv) < 2:
    print("Error: Missing required argument")
    sys.exit(1)
```

#### 3. Use Appropriate Timeouts
- Short timeouts for simple operations (10-30 seconds)
- Longer timeouts for complex processing (60-300 seconds)
- Consider the operation complexity

### Performance Optimization

#### 1. Minimize External Dependencies
```bash
# Good: Use built-in commands
echo "Current time: $(date)"

# Avoid: External tools that might not be available
# fancy_date_formatter --iso
```

#### 2. Efficient Resource Usage
```python
# Good: Process data in chunks
def process_large_file(filename):
    with open(filename, 'r') as f:
        for line in f:  # Process line by line
            yield process_line(line)
```

## Integration Examples

### Workflow Automation
The script tool can be integrated into automation workflows:

```json
{
  "workflow": "system_health_check",
  "steps": [
    {
      "name": "Check System Info",
      "tool": "script_tool.run_predefined_script",
      "args": {"script_name": "system_info"}
    },
    {
      "name": "Check Disk Usage",
      "tool": "script_tool.run_predefined_script", 
      "args": {"script_name": "disk_usage"}
    },
    {
      "name": "Test Network",
      "tool": "script_tool.run_predefined_script",
      "args": {"script_name": "network_test"}
    }
  ]
}
```

### Custom Monitoring
```json
{
  "name": "script_tool.run_custom_script",
  "arguments": {
    "script_content": "#!/bin/bash\necho 'System Health Report'\necho '=================='\necho 'Uptime:' $(uptime)\necho 'Load Average:' $(cat /proc/loadavg)\necho 'Memory:' $(free -h | grep Mem)\necho 'Disk:' $(df -h / | tail -1)",
    "language": "bash",
    "timeout": 30
  }
}
```

## Troubleshooting

### Common Issues

#### 1. Script Timeout
**Problem**: Script execution times out
**Solution**: 
- Increase timeout parameter
- Optimize script for better performance
- Break complex scripts into smaller parts

#### 2. Permission Denied
**Problem**: Script cannot access files or execute commands
**Solution**:
- Check file permissions
- Use alternative approaches that don't require elevated privileges
- Use predefined scripts when possible

#### 3. Command Not Found
**Problem**: Script uses commands not available on the system
**Solution**:
- Check command availability first
- Use alternative commands
- Provide fallback options

#### 4. Output Too Large
**Problem**: Script generates too much output
**Solution**:
- Limit output in the script
- Use pagination or filtering
- Process data in smaller chunks

### Debugging Scripts

#### 1. Add Debug Output
```bash
#!/bin/bash
set -x  # Enable debug mode
echo "Starting script execution"
# Your script here
echo "Script completed"
```

#### 2. Check Return Codes
```python
import subprocess
import sys

try:
    result = subprocess.run(['command'], capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Command failed with code {result.returncode}")
        print(f"Error: {result.stderr}")
        sys.exit(1)
    print(f"Success: {result.stdout}")
except Exception as e:
    print(f"Exception: {e}")
    sys.exit(1)
```

## Contributing

### Adding Predefined Scripts

To add new predefined scripts, modify the `PREDEFINED_SCRIPTS` dictionary in `script_tool.py`:

```python
PREDEFINED_SCRIPTS = {
    "new_script": {
        "description": "Description of what the script does",
        "script": "command to execute"
    }
}
```

### Guidelines for Predefined Scripts
1. **Security**: Must be completely safe to execute
2. **Reliability**: Should work across different environments
3. **Usefulness**: Should serve a common, practical purpose
4. **Documentation**: Include clear description
5. **Testing**: Test thoroughly before adding

This tool provides a secure, flexible foundation for script execution in the Orchestra Template Engine ecosystem while maintaining safety and reliability.
