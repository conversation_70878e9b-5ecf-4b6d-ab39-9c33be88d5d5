"""Add namespace and tool tables

Revision ID: 9d0871905a12
Revises: 83d55a1e53cf
Create Date: 2025-06-01 20:02:19.549824

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '9d0871905a12'
down_revision: Union[str, None] = '83d55a1e53cf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('namespace',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_namespace_name'), 'namespace', ['name'], unique=True)
    op.create_table('tool',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('full_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('namespace_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['namespace_id'], ['namespace.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tool_full_name'), 'tool', ['full_name'], unique=True)
    op.create_index(op.f('ix_tool_name'), 'tool', ['name'], unique=False)
    op.alter_column('workflow', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('workflow', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.drop_index(op.f('ix_tool_name'), table_name='tool')
    op.drop_index(op.f('ix_tool_full_name'), table_name='tool')
    op.drop_table('tool')
    op.drop_index(op.f('ix_namespace_name'), table_name='namespace')
    op.drop_table('namespace')
    # ### end Alembic commands ###
