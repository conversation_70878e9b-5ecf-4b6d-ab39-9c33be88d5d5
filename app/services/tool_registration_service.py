"""
tool_registration_service.py

Service for registering tools and namespaces in the database.

Author: <PERSON>
"""
import logging
from typing import Dict, Callable, List

# Import with try/catch to handle different execution contexts
try:
    from repository import NamespaceRepository, ToolRepository
    from mcp_server.tools import ToolManager
except ImportError:
    from app.repository import NamespaceRepository, ToolRepository
    from app.mcp_server.tools import ToolManager


logger = logging.getLogger(__name__)


class ToolRegistrationService:
    """
    Service for registering tools and namespaces in the database.
    
    This service:
    1. Scans all registered tools from the ToolManager
    2. Creates/updates namespaces and tools in the database
    3. Deactivates tools that are no longer present
    """

    def __init__(self):
        self.namespace_repo = NamespaceRepository()
        self.tool_repo = ToolRepository()

    def register_all_tools(self, tool_manager: ToolManager) -> Dict[str, int]:
        """
        Register all tools from the tool manager in the database.

        Args:
            tool_manager: The tool manager containing all discovered tools

        Returns:
            Dictionary with registration statistics
        """
        logger.info("Starting tool registration process...")
        
        stats = {
            'namespaces_created': 0,
            'namespaces_updated': 0,
            'tools_created': 0,
            'tools_updated': 0,
            'tools_deactivated': 0
        }

        try:
            # Get all tool methods from the registry
            all_tool_methods = tool_manager.get_all_tool_methods()
            logger.info(f"Found {len(all_tool_methods)} tool methods to register")

            # Group tools by namespace
            namespaces_data = self._group_tools_by_namespace(tool_manager, all_tool_methods)
            
            # Register namespaces and tools
            active_tool_full_names = []
            
            for namespace_name, tools_data in namespaces_data.items():
                # Register namespace
                namespace_stats = self._register_namespace(namespace_name, tools_data['description'])
                stats['namespaces_created'] += namespace_stats['created']
                stats['namespaces_updated'] += namespace_stats['updated']
                
                # Get the namespace
                namespace = self.namespace_repo.find_by_name(namespace_name)
                if not namespace:
                    logger.error(f"Failed to find or create namespace: {namespace_name}")
                    continue
                
                # Register tools for this namespace
                for tool_data in tools_data['tools']:
                    tool_stats = self._register_tool(tool_data, namespace.id)
                    stats['tools_created'] += tool_stats['created']
                    stats['tools_updated'] += tool_stats['updated']
                    
                    active_tool_full_names.append(tool_data['full_name'])

            # Deactivate tools that are no longer present
            deactivated_count = self._deactivate_missing_tools(active_tool_full_names)
            stats['tools_deactivated'] = deactivated_count

            logger.info(f"Tool registration completed. Stats: {stats}")
            return stats

        except Exception as e:
            logger.error(f"Error during tool registration: {str(e)}")
            raise

    def _group_tools_by_namespace(
        self, 
        tool_manager: ToolManager, 
        all_tool_methods: Dict[str, Callable]
    ) -> Dict[str, Dict]:
        """
        Group tools by their namespace.

        Args:
            tool_manager: The tool manager
            all_tool_methods: Dictionary of all tool methods

        Returns:
            Dictionary grouped by namespace
        """
        namespaces_data = {}
        
        # Get tool instances to access their descriptions
        registry = tool_manager.get_registry()
        
        for full_method_name, method in all_tool_methods.items():
            # Parse namespace and tool name from full method name
            if '.' not in full_method_name:
                logger.warning(f"Skipping tool method with invalid name format: {full_method_name}")
                continue
                
            namespace_name, tool_name = full_method_name.split('.', 1)
            
            # Get tool instance for description
            tool_instance = registry.get_tool(namespace_name)
            namespace_description = tool_instance.tool_description if tool_instance else None
            tool_description = getattr(method, '_tool_description', None)
            
            # Initialize namespace data if not exists
            if namespace_name not in namespaces_data:
                namespaces_data[namespace_name] = {
                    'description': namespace_description,
                    'tools': []
                }
            
            # Add tool data
            tool_data = {
                'name': tool_name,
                'full_name': full_method_name,
                'description': tool_description
            }
            namespaces_data[namespace_name]['tools'].append(tool_data)
        
        return namespaces_data

    def _register_namespace(self, name: str, description: str) -> Dict[str, int]:
        """
        Register a namespace in the database.

        Args:
            name: Namespace name
            description: Namespace description

        Returns:
            Dictionary with creation/update stats
        """
        stats = {'created': 0, 'updated': 0}
        
        try:
            existing = self.namespace_repo.find_by_name(name)
            
            if existing:
                # Update description if it has changed
                if existing.description != description:
                    self.namespace_repo.update(existing.id, {
                        'description': description,
                        'is_active': True
                    })
                    stats['updated'] = 1
                    logger.debug(f"Updated namespace: {name}")
                elif not existing.is_active:
                    # Reactivate if it was deactivated
                    self.namespace_repo.update(existing.id, {'is_active': True})
                    stats['updated'] = 1
                    logger.debug(f"Reactivated namespace: {name}")
            else:
                # Create new namespace
                self.namespace_repo.create({
                    'name': name,
                    'description': description,
                    'is_active': True
                })
                stats['created'] = 1
                logger.debug(f"Created namespace: {name}")
                
        except Exception as e:
            logger.error(f"Error registering namespace {name}: {str(e)}")
            
        return stats

    def _register_tool(self, tool_data: Dict, namespace_id) -> Dict[str, int]:
        """
        Register a tool in the database.

        Args:
            tool_data: Tool data dictionary
            namespace_id: UUID of the namespace

        Returns:
            Dictionary with creation/update stats
        """
        stats = {'created': 0, 'updated': 0}
        
        try:
            existing = self.tool_repo.find_by_full_name(tool_data['full_name'])
            
            if existing:
                # Update if description changed or if it was deactivated
                update_data = {}
                if existing.description != tool_data['description']:
                    update_data['description'] = tool_data['description']
                if not existing.is_active:
                    update_data['is_active'] = True
                    
                if update_data:
                    self.tool_repo.update(existing.id, update_data)
                    stats['updated'] = 1
                    logger.debug(f"Updated tool: {tool_data['full_name']}")
            else:
                # Create new tool
                self.tool_repo.create({
                    'name': tool_data['name'],
                    'full_name': tool_data['full_name'],
                    'description': tool_data['description'],
                    'namespace_id': namespace_id,
                    'is_active': True
                })
                stats['created'] = 1
                logger.debug(f"Created tool: {tool_data['full_name']}")
                
        except Exception as e:
            logger.error(f"Error registering tool {tool_data['full_name']}: {str(e)}")
            
        return stats

    def _deactivate_missing_tools(self, active_tool_full_names: List[str]) -> int:
        """
        Deactivate tools that are no longer present in the codebase.

        Args:
            active_tool_full_names: List of full names of tools that should remain active

        Returns:
            Number of tools deactivated
        """
        try:
            missing_tools = self.tool_repo.get_tools_not_in_list(active_tool_full_names)
            deactivated_count = 0
            
            for tool in missing_tools:
                self.tool_repo.update(tool.id, {'is_active': False})
                deactivated_count += 1
                logger.debug(f"Deactivated missing tool: {tool.full_name}")
            
            if deactivated_count > 0:
                logger.info(f"Deactivated {deactivated_count} missing tools")
                
            return deactivated_count
            
        except Exception as e:
            logger.error(f"Error deactivating missing tools: {str(e)}")
            return 0
