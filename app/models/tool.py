"""
tool.py

Database model for tools.

Author: <PERSON>
"""
from typing import Optional, TYPE_CHECKING
from uuid import UUID
from sqlmodel import Field, Relationship

from .base import BaseModel

if TYPE_CHECKING:
    from .namespace import Namespace


class Tool(BaseModel, table=True):
    """
    Model representing a tool method.
    
    Each tool method (like 'clone_repository', 'list_workflows') is stored
    as a separate record associated with its namespace.
    """
    
    name: str = Field(
        index=True, 
        description="Name of the tool method (e.g., 'clone_repository')"
    )
    full_name: str = Field(
        unique=True,
        index=True,
        description="Full name including namespace (e.g., 'github_tool.clone_repository')"
    )
    description: Optional[str] = Field(
        default=None, 
        description="Description of what this tool does"
    )
    is_active: bool = Field(
        default=True, 
        description="Whether this tool is currently active"
    )
    
    # Foreign key to namespace
    namespace_id: UUID = Field(
        foreign_key="namespace.id",
        description="ID of the namespace this tool belongs to"
    )
    
    # Relationship to namespace
    namespace: "Namespace" = Relationship(back_populates="tools")
    
    def __str__(self) -> str:
        return f"Tool(full_name='{self.full_name}')"
    
    def __repr__(self) -> str:
        return f"Tool(id={self.id}, full_name='{self.full_name}', is_active={self.is_active})"
