"""
namespace_repository.py

Repository for Namespace model operations.

Author: <PERSON>
"""
from typing import Optional, Sequence

# Import with try/catch to handle different execution contexts
try:
    from models.namespace import Namespace
except ImportError:
    from app.models.namespace import Namespace
from .base_repository import BaseRepository


class NamespaceRepository(BaseRepository[Namespace]):
    """Repository for Namespace model with specific business logic."""

    model = Namespace

    def find_by_name(self, name: str) -> Optional[Namespace]:
        """
        Find a namespace by its name.

        Args:
            name: Namespace name

        Returns:
            Namespace instance or None if not found
        """
        return self.find_one_by(name=name)

    def find_active_namespaces(self) -> Sequence[Namespace]:
        """
        Find all active namespaces.

        Returns:
            List of active namespaces
        """
        return self.find_by(is_active=True)

    def search_namespaces(self, search_term: str, limit: Optional[int] = None) -> Sequence[Namespace]:
        """
        Search namespaces by name and description.

        Args:
            search_term: Text to search for
            limit: Maximum number of results

        Returns:
            List of matching namespaces
        """
        return self.search(
            search_fields=['name', 'description'],
            search_term=search_term,
            limit=limit
        )

    def get_or_create_namespace(self, name: str, description: Optional[str] = None) -> Namespace:
        """
        Get an existing namespace or create a new one.

        Args:
            name: Namespace name
            description: Optional description

        Returns:
            Namespace instance (existing or newly created)
        """
        existing = self.find_by_name(name)
        if existing:
            return existing
        
        return self.create({
            'name': name,
            'description': description,
            'is_active': True
        })

    def deactivate_namespace(self, name: str) -> Optional[Namespace]:
        """
        Deactivate a namespace by name.

        Args:
            name: Namespace name

        Returns:
            Updated namespace or None if not found
        """
        namespace = self.find_by_name(name)
        if namespace:
            return self.update(namespace.id, {'is_active': False})
        return None

    def activate_namespace(self, name: str) -> Optional[Namespace]:
        """
        Activate a namespace by name.

        Args:
            name: Namespace name

        Returns:
            Updated namespace or None if not found
        """
        namespace = self.find_by_name(name)
        if namespace:
            return self.update(namespace.id, {'is_active': True})
        return None
