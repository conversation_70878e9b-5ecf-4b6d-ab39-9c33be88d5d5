# Tool Registration System

The Orchestra Template Engine includes an automatic tool registration system that maintains a database record of all available tools and their namespaces.

## Overview

The tool registration system:
- **Automatically discovers** all tools and their methods on server startup
- **Creates database records** for namespaces and tools
- **Maintains synchronization** between codebase and database
- **Deactivates missing tools** when they're removed from the codebase
- **Reactivates tools** when they're added back

## Database Schema

### Namespace Table

Stores tool namespaces (e.g., `github_tool`, `workflow_tool`):

```sql
CREATE TABLE namespace (
    id UUID PRIMARY KEY,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    name VARCHAR UNIQUE NOT NULL,
    description VARCHAR,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);
```

### Tool Table

Stores individual tool methods:

```sql
CREATE TABLE tool (
    id UUID PRIMARY KEY,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    name VA<PERSON>HAR NOT NULL,
    full_name VA<PERSON>HAR UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    namespace_id UUID REFERENCES namespace(id)
);
```

## Tool Registration Process

### 1. Server Startup

When the MCP server starts, the `ToolRegistrationService` automatically:

1. **Scans all tool classes** using the existing `ToolManager`
2. **Groups tools by namespace** (tool_name becomes namespace)
3. **Creates/updates namespaces** in the database
4. **Creates/updates tools** for each method
5. **Deactivates missing tools** that no longer exist in code

### 2. Namespace Creation

For each tool class (e.g., `GitHubTool`):
- **Namespace name**: Uses `tool_name` property (e.g., `"github_tool"`)
- **Description**: Uses `tool_description` property
- **Tools**: All methods decorated with `@tool_method`

### 3. Tool Registration

For each tool method:
- **Name**: Method name (e.g., `"clone_repository"`)
- **Full name**: Namespace + method (e.g., `"github_tool.clone_repository"`)
- **Description**: Method docstring or `_tool_description`
- **Namespace**: Associated with parent namespace

## Example Registration

Given this tool class:

```python
class GitHubTool(BaseTool):
    @property
    def tool_name(self) -> str:
        return "github_tool"
    
    @property 
    def tool_description(self) -> str:
        return "Clone GitHub repositories and templates"
    
    @tool_method(name="clone_repository")
    async def clone_repository(self, repository_url: str) -> dict:
        """Clone a GitHub repository."""
        # Implementation
```

The registration creates:

**Namespace Record:**
- `name`: `"github_tool"`
- `description`: `"Clone GitHub repositories and templates"`
- `is_active`: `True`

**Tool Record:**
- `name`: `"clone_repository"`
- `full_name`: `"github_tool.clone_repository"`
- `description`: `"Clone a GitHub repository."`
- `namespace_id`: Reference to github_tool namespace
- `is_active`: `True`

## Registration Statistics

The registration process returns statistics:

```python
{
    'namespaces_created': 3,      # New namespaces added
    'namespaces_updated': 0,      # Existing namespaces updated
    'tools_created': 7,           # New tools added
    'tools_updated': 0,           # Existing tools updated
    'tools_deactivated': 0        # Tools marked as inactive
}
```

## Querying Registered Tools

### Using Repositories

```python
from app.repository import NamespaceRepository, ToolRepository

namespace_repo = NamespaceRepository()
tool_repo = ToolRepository()

# Get all active namespaces
namespaces = namespace_repo.find_active_namespaces()

# Get all tools in a namespace
github_namespace = namespace_repo.find_by_name("github_tool")
github_tools = tool_repo.find_by_namespace_id(github_namespace.id)

# Find specific tool
clone_tool = tool_repo.find_by_full_name("github_tool.clone_repository")

# Search tools
search_results = tool_repo.search_tools("clone")
```

### Repository Methods

**NamespaceRepository:**
- `find_by_name(name)` - Find namespace by name
- `find_active_namespaces()` - Get all active namespaces
- `get_or_create_namespace(name, description)` - Get or create namespace
- `search_namespaces(term)` - Search by name/description

**ToolRepository:**
- `find_by_full_name(full_name)` - Find tool by full name
- `find_by_namespace_id(namespace_id)` - Get tools in namespace
- `find_active_tools()` - Get all active tools
- `get_or_create_tool(...)` - Get or create tool
- `search_tools(term)` - Search by name/description

## Lifecycle Management

### Tool Addition
When a new tool is added to the codebase:
1. Next server restart detects the new tool
2. Creates namespace record (if new namespace)
3. Creates tool record
4. Tool becomes available immediately

### Tool Removal
When a tool is removed from the codebase:
1. Next server restart detects missing tool
2. Sets `is_active = False` on the tool record
3. Tool is no longer available but record is preserved

### Tool Restoration
When a previously removed tool is added back:
1. Server restart detects the tool again
2. Sets `is_active = True` on existing record
3. Tool becomes available again with preserved history

## Integration Points

### Server Startup
The registration happens automatically in `app/server.py`:

```python
# Register tools and namespaces in database
registration_service = ToolRegistrationService()
stats = registration_service.register_all_tools(tool_manager)
```

### Database Migrations
The tables are created via Alembic migration:
- Migration: `9d0871905a12_add_namespace_and_tool_tables.py`
- Run: `alembic upgrade head`

## Benefits

1. **Audit Trail**: Complete history of tool availability
2. **API Discovery**: Database-driven tool discovery for APIs
3. **Monitoring**: Track tool usage and availability
4. **Documentation**: Centralized tool metadata
5. **Synchronization**: Automatic sync between code and database
