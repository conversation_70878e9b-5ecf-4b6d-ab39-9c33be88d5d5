"""
namespace.py

Database model for tool namespaces.

Author: <PERSON>
"""
from typing import Optional, List, TYPE_CHECKING
from sqlmodel import Field, Relationship

from .base import BaseModel

if TYPE_CHECKING:
    from .tool import Tool


class Namespace(BaseModel, table=True):
    """
    Model representing a tool namespace.
    
    A namespace groups related tools together. For example, 'github_tool' 
    namespace contains tools like 'clone_repository', 'clone_template', etc.
    """
    
    name: str = Field(
        unique=True, 
        index=True, 
        description="Unique name of the namespace (e.g., 'github_tool')"
    )
    description: Optional[str] = Field(
        default=None, 
        description="Description of what this namespace provides"
    )
    is_active: bool = Field(
        default=True, 
        description="Whether this namespace is currently active"
    )
    
    # Relationship to tools
    tools: List["Tool"] = Relationship(back_populates="namespace")
    
    def __str__(self) -> str:
        return f"Namespace(name='{self.name}')"
    
    def __repr__(self) -> str:
        return f"Namespace(id={self.id}, name='{self.name}', is_active={self.is_active})"
