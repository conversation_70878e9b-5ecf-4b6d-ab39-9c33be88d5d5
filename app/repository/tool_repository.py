"""
tool_repository.py

Repository for Tool model operations.

Author: <PERSON>
"""
from typing import Optional, Sequence, List
from uuid import UUID

# Import with try/catch to handle different execution contexts
try:
    from models.tool import Tool
except ImportError:
    from app.models.tool import Tool
from .base_repository import BaseRepository


class ToolRepository(BaseRepository[Tool]):
    """Repository for Tool model with specific business logic."""

    model = Tool

    def find_by_name(self, name: str) -> Sequence[Tool]:
        """
        Find tools by their name (may return multiple tools with same name in different namespaces).

        Args:
            name: Tool name

        Returns:
            List of tool instances
        """
        return self.find_by(name=name)

    def find_by_full_name(self, full_name: str) -> Optional[Tool]:
        """
        Find a tool by its full name (namespace.tool_name).

        Args:
            full_name: Full tool name (e.g., 'github_tool.clone_repository')

        Returns:
            Tool instance or None if not found
        """
        return self.find_one_by(full_name=full_name)

    def find_by_namespace_id(self, namespace_id: UUID) -> Sequence[Tool]:
        """
        Find all tools belonging to a specific namespace.

        Args:
            namespace_id: UUID of the namespace

        Returns:
            List of tools in the namespace
        """
        return self.find_by(namespace_id=namespace_id)

    def find_active_tools(self) -> Sequence[Tool]:
        """
        Find all active tools.

        Returns:
            List of active tools
        """
        return self.find_by(is_active=True)

    def find_active_tools_by_namespace_id(self, namespace_id: UUID) -> Sequence[Tool]:
        """
        Find all active tools in a specific namespace.

        Args:
            namespace_id: UUID of the namespace

        Returns:
            List of active tools in the namespace
        """
        with self._get_session() as session:
            from sqlmodel import select, and_
            statement = select(self.model).where(
                and_(
                    self.model.namespace_id == namespace_id,
                    self.model.is_active == True
                )
            )
            return session.exec(statement).all()

    def search_tools(self, search_term: str, limit: Optional[int] = None) -> Sequence[Tool]:
        """
        Search tools by name, full_name, and description.

        Args:
            search_term: Text to search for
            limit: Maximum number of results

        Returns:
            List of matching tools
        """
        return self.search(
            search_fields=['name', 'full_name', 'description'],
            search_term=search_term,
            limit=limit
        )

    def get_or_create_tool(
        self, 
        name: str, 
        full_name: str, 
        namespace_id: UUID, 
        description: Optional[str] = None
    ) -> Tool:
        """
        Get an existing tool or create a new one.

        Args:
            name: Tool name
            full_name: Full tool name (namespace.tool_name)
            namespace_id: UUID of the namespace
            description: Optional description

        Returns:
            Tool instance (existing or newly created)
        """
        existing = self.find_by_full_name(full_name)
        if existing:
            # Update the tool if it exists but ensure it's active
            if not existing.is_active:
                return self.update(existing.id, {'is_active': True})
            return existing
        
        return self.create({
            'name': name,
            'full_name': full_name,
            'namespace_id': namespace_id,
            'description': description,
            'is_active': True
        })

    def deactivate_tool(self, full_name: str) -> Optional[Tool]:
        """
        Deactivate a tool by full name.

        Args:
            full_name: Full tool name

        Returns:
            Updated tool or None if not found
        """
        tool = self.find_by_full_name(full_name)
        if tool:
            return self.update(tool.id, {'is_active': False})
        return None

    def deactivate_tools_by_namespace_id(self, namespace_id: UUID) -> List[Tool]:
        """
        Deactivate all tools in a specific namespace.

        Args:
            namespace_id: UUID of the namespace

        Returns:
            List of deactivated tools
        """
        tools = self.find_by_namespace_id(namespace_id)
        deactivated_tools = []
        
        for tool in tools:
            if tool.is_active:
                updated_tool = self.update(tool.id, {'is_active': False})
                if updated_tool:
                    deactivated_tools.append(updated_tool)
        
        return deactivated_tools

    def get_tools_not_in_list(self, full_names: List[str]) -> Sequence[Tool]:
        """
        Get all active tools that are not in the provided list of full names.
        This is useful for finding tools that should be deactivated.

        Args:
            full_names: List of full tool names that should remain active

        Returns:
            List of tools not in the provided list
        """
        with self._get_session() as session:
            from sqlmodel import select, and_, not_
            statement = select(self.model).where(
                and_(
                    self.model.is_active == True,
                    not_(self.model.full_name.in_(full_names))
                )
            )
            return session.exec(statement).all()
