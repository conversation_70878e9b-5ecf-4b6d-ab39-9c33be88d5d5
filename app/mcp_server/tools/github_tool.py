"""
github_tool.py

Tool for GitHub operations including repository cloning and template cloning with Copier.

Author: <PERSON>
"""
import asyncio
import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse

from .base_tool import BaseTool, tool_method


class GitHubTool(BaseTool):
    """Tool for GitHub repository and template operations."""

    @property
    def tool_name(self) -> str:
        return "github_tool"

    @property
    def tool_description(self) -> str:
        return "Clone GitHub repositories and templates using Git and Copier"

    async def _run_command(self, command: str, cwd: Optional[str] = None, timeout: int = 300) -> Dict[str, Any]:
        """
        Run a shell command asynchronously with timeout and error handling.
        
        Args:
            command: Command to execute
            cwd: Working directory for the command
            timeout: Maximum execution time in seconds
            
        Returns:
            Dictionary with success status, output, and error information
        """
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd,
                limit=1024 * 1024 * 10  # 10MB limit for output
            )

            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=timeout
                )

                return {
                    "success": process.returncode == 0,
                    "return_code": process.returncode,
                    "stdout": stdout.decode("utf-8", errors="replace"),
                    "stderr": stderr.decode("utf-8", errors="replace"),
                    "timeout": False,
                }

            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return {
                    "success": False,
                    "return_code": -1,
                    "stdout": "",
                    "stderr": f"Command timed out after {timeout} seconds",
                    "timeout": True,
                }

        except Exception as e:
            return {
                "success": False,
                "return_code": -1,
                "stdout": "",
                "stderr": f"Failed to execute command: {str(e)}",
                "timeout": False,
            }

    def _validate_github_url(self, url: str) -> Dict[str, Any]:
        """
        Validate and parse a GitHub URL (supports both HTTPS and SSH formats).

        Args:
            url: GitHub repository URL (HTTPS or SSH format)

        Returns:
            Dictionary with validation result and parsed components
        """
        try:
            # Handle SSH URLs (**************:owner/repo.git)
            if url.startswith('**************:'):
                ssh_part = url[len('**************:'):]

                # Remove .git suffix if present
                if ssh_part.endswith('.git'):
                    ssh_part = ssh_part[:-4]

                # Split owner/repo
                path_parts = ssh_part.split('/')
                if len(path_parts) < 2:
                    return {
                        "valid": False,
                        "error": "Invalid GitHub SSH URL format. Expected: **************:owner/repo.git"
                    }

                owner = path_parts[0]
                repo = path_parts[1]

                return {
                    "valid": True,
                    "owner": owner,
                    "repo": repo,
                    "clone_url": f"https://github.com/{owner}/{repo}.git"
                }

            # Handle HTTPS URLs
            parsed = urlparse(url)

            # Check if it's a GitHub URL
            if parsed.netloc not in ['github.com', 'www.github.com']:
                return {
                    "valid": False,
                    "error": "URL must be a GitHub repository URL (HTTPS or SSH format)"
                }

            # Extract owner and repo from path
            path_parts = parsed.path.strip('/').split('/')
            if len(path_parts) < 2:
                return {
                    "valid": False,
                    "error": "Invalid GitHub URL format. Expected: https://github.com/owner/<NAME_EMAIL>:owner/repo.git"
                }

            owner = path_parts[0]
            repo = path_parts[1]

            # Remove .git suffix if present
            if repo.endswith('.git'):
                repo = repo[:-4]

            return {
                "valid": True,
                "owner": owner,
                "repo": repo,
                "clone_url": f"https://github.com/{owner}/{repo}.git"
            }

        except Exception as e:
            return {
                "valid": False,
                "error": f"Failed to parse URL: {str(e)}"
            }

    def _ensure_directory_exists(self, path: str) -> bool:
        """
        Ensure a directory exists, creating it if necessary.
        
        Args:
            path: Directory path
            
        Returns:
            True if directory exists or was created successfully
        """
        try:
            os.makedirs(path, exist_ok=True)
            return True
        except Exception:
            return False

    @tool_method(name="clone_repository")
    async def clone_repository(
        self,
        repository_url: str,
        destination_path: str,
        branch: Optional[str] = None,
        depth: Optional[int] = None,
        recursive: bool = False
    ) -> Dict[str, Any]:
        """
        Clone a GitHub repository to a local directory.
        
        Args:
            repository_url: GitHub repository URL (e.g., https://github.com/owner/repo)
            destination_path: Local path where the repository should be cloned
            branch: Specific branch to clone (optional)
            depth: Shallow clone depth (optional, for faster cloning)
            recursive: Whether to clone submodules recursively
            
        Returns:
            Dictionary with operation result and details
        """
        try:
            # Validate GitHub URL
            url_validation = self._validate_github_url(repository_url)
            if not url_validation["valid"]:
                return {
                    "success": False,
                    "error": url_validation["error"],
                    "repository_url": repository_url,
                    "destination_path": destination_path
                }
            
            clone_url = url_validation["clone_url"]
            repo_name = url_validation["repo"]
            
            # Resolve destination path
            dest_path = Path(destination_path).expanduser().resolve()
            
            # Check if destination already exists
            if dest_path.exists():
                if dest_path.is_dir() and any(dest_path.iterdir()):
                    return {
                        "success": False,
                        "error": f"Destination directory '{dest_path}' already exists and is not empty",
                        "repository_url": repository_url,
                        "destination_path": str(dest_path)
                    }
                elif dest_path.is_file():
                    return {
                        "success": False,
                        "error": f"Destination path '{dest_path}' is a file, not a directory",
                        "repository_url": repository_url,
                        "destination_path": str(dest_path)
                    }
            
            # Ensure parent directory exists
            if not self._ensure_directory_exists(str(dest_path.parent)):
                return {
                    "success": False,
                    "error": f"Failed to create parent directory '{dest_path.parent}'",
                    "repository_url": repository_url,
                    "destination_path": str(dest_path)
                }
            
            # Build git clone command
            git_command = ["git", "clone"]
            
            if branch:
                git_command.extend(["--branch", branch])
            
            if depth:
                git_command.extend(["--depth", str(depth)])
            
            if recursive:
                git_command.append("--recursive")
            
            git_command.extend([clone_url, str(dest_path)])
            
            # Execute git clone
            command_str = " ".join(f'"{arg}"' if " " in arg else arg for arg in git_command)
            result = await self._run_command(command_str, timeout=600)  # 10 minute timeout
            
            if result["success"]:
                # Get repository information
                info_result = await self._run_command(
                    "git log --oneline -1", 
                    cwd=str(dest_path)
                )
                
                latest_commit = info_result["stdout"].strip() if info_result["success"] else "Unknown"
                
                return {
                    "success": True,
                    "message": f"Successfully cloned repository '{repo_name}'",
                    "repository_url": repository_url,
                    "destination_path": str(dest_path),
                    "branch": branch or "default",
                    "latest_commit": latest_commit,
                    "clone_options": {
                        "depth": depth,
                        "recursive": recursive
                    }
                }
            else:
                return {
                    "success": False,
                    "error": f"Git clone failed: {result['stderr']}",
                    "repository_url": repository_url,
                    "destination_path": str(dest_path),
                    "command_output": result["stdout"]
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error during repository cloning: {str(e)}",
                "repository_url": repository_url,
                "destination_path": destination_path
            }

    @tool_method(name="clone_template")
    async def clone_template(
        self,
        template_url: str,
        destination_path: str,
        template_values: Optional[Dict[str, Any]] = None,
        branch: Optional[str] = None,
        exclude_patterns: Optional[List[str]] = None,
        overwrite: bool = False
    ) -> Dict[str, Any]:
        """
        Clone a GitHub template repository using Copier.
        
        Args:
            template_url: GitHub repository URL containing the Copier template
            destination_path: Local path where the template should be rendered
            template_values: Dictionary of values to use for template rendering
            branch: Specific branch/tag/commit to use (optional)
            exclude_patterns: List of file patterns to exclude from copying
            overwrite: Whether to overwrite existing files
            
        Returns:
            Dictionary with operation result and details
        """
        try:
            # Validate GitHub URL
            url_validation = self._validate_github_url(template_url)
            if not url_validation["valid"]:
                return {
                    "success": False,
                    "error": url_validation["error"],
                    "template_url": template_url,
                    "destination_path": destination_path
                }
            
            repo_name = url_validation["repo"]
            
            # Resolve destination path
            dest_path = Path(destination_path).expanduser().resolve()
            
            # Check if destination exists and handle overwrite
            if dest_path.exists():
                if not overwrite:
                    if dest_path.is_dir() and any(dest_path.iterdir()):
                        return {
                            "success": False,
                            "error": f"Destination directory '{dest_path}' already exists and is not empty. Use overwrite=True to force.",
                            "template_url": template_url,
                            "destination_path": str(dest_path)
                        }
                    elif dest_path.is_file():
                        return {
                            "success": False,
                            "error": f"Destination path '{dest_path}' is a file, not a directory",
                            "template_url": template_url,
                            "destination_path": str(dest_path)
                        }
            
            # Ensure parent directory exists
            if not self._ensure_directory_exists(str(dest_path.parent)):
                return {
                    "success": False,
                    "error": f"Failed to create parent directory '{dest_path.parent}'",
                    "template_url": template_url,
                    "destination_path": str(dest_path)
                }
            
            # Build copier command
            copier_command = ["copier", "copy"]
            
            # Add template URL with optional branch/tag
            if branch:
                template_ref = f"{template_url}@{branch}"
            else:
                template_ref = template_url
            
            copier_command.append(template_ref)
            copier_command.append(str(dest_path))
            
            # Add template values as command line arguments
            if template_values:
                for key, value in template_values.items():
                    copier_command.extend(["--data", f"{key}={value}"])
            
            # Add exclude patterns
            if exclude_patterns:
                for pattern in exclude_patterns:
                    copier_command.extend(["--skip", pattern])
            
            # Add overwrite flag
            if overwrite:
                copier_command.append("--overwrite")
            
            # Add non-interactive flag
            copier_command.append("--defaults")
            
            # Execute copier command
            command_str = " ".join(f'"{arg}"' if " " in arg else arg for arg in copier_command)
            result = await self._run_command(command_str, timeout=600)  # 10 minute timeout
            
            if result["success"]:
                # Count generated files
                try:
                    file_count = sum(1 for _ in dest_path.rglob("*") if _.is_file())
                except Exception:
                    file_count = "Unknown"
                
                return {
                    "success": True,
                    "message": f"Successfully generated template '{repo_name}' using Copier",
                    "template_url": template_url,
                    "destination_path": str(dest_path),
                    "branch": branch or "default",
                    "template_values": template_values or {},
                    "exclude_patterns": exclude_patterns or [],
                    "files_generated": file_count,
                    "overwrite": overwrite
                }
            else:
                return {
                    "success": False,
                    "error": f"Copier template generation failed: {result['stderr']}",
                    "template_url": template_url,
                    "destination_path": str(dest_path),
                    "command_output": result["stdout"]
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Unexpected error during template cloning: {str(e)}",
                "template_url": template_url,
                "destination_path": destination_path
            }

    @tool_method(name="list_repository_info")
    async def list_repository_info(self, repository_path: str) -> Dict[str, Any]:
        """
        Get information about a local Git repository.
        
        Args:
            repository_path: Path to the local Git repository
            
        Returns:
            Dictionary with repository information
        """
        try:
            repo_path = Path(repository_path).expanduser().resolve()
            
            if not repo_path.exists():
                return {
                    "success": False,
                    "error": f"Repository path '{repo_path}' does not exist",
                    "repository_path": str(repo_path)
                }
            
            if not (repo_path / ".git").exists():
                return {
                    "success": False,
                    "error": f"Path '{repo_path}' is not a Git repository",
                    "repository_path": str(repo_path)
                }
            
            # Get repository information
            commands = {
                "remote_url": "git remote get-url origin",
                "current_branch": "git branch --show-current",
                "latest_commit": "git log --oneline -1",
                "status": "git status --porcelain",
                "branches": "git branch -a"
            }
            
            info = {"repository_path": str(repo_path)}
            
            for key, command in commands.items():
                result = await self._run_command(command, cwd=str(repo_path))
                if result["success"]:
                    info[key] = result["stdout"].strip()
                else:
                    info[key] = f"Error: {result['stderr']}"
            
            # Parse status for cleaner output
            if info.get("status"):
                status_lines = info["status"].split("\n")
                info["modified_files"] = len([line for line in status_lines if line.strip()])
                info["is_clean"] = len(status_lines) == 0 or all(not line.strip() for line in status_lines)
            else:
                info["modified_files"] = 0
                info["is_clean"] = True
            
            return {
                "success": True,
                "repository_info": info
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to get repository information: {str(e)}",
                "repository_path": repository_path
            }
